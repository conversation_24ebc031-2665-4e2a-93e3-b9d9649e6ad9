import { NextRequest } from 'next/server'
import { scrapeWebPage } from '@/lib/scraper'
import { streamKnowledgeCards } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const { input, type } = await request.json()

    if (!input) {
      return new Response('Missing input (URL or text)', { status: 400 })
    }

    let contentToProcess: string
    let title: string

    if (type === 'url') {
      const scrapedContent = await scrapeWebPage(input)
      contentToProcess = scrapedContent.textContent
      title = scrapedContent.title
    } else {
      contentToProcess = input
      title = '文本分析'
    }

    if (!contentToProcess) {
      return new Response('Failed to get content to process', { status: 500 })
    }
    
    // 获取来自OpenAI的流
    const aiStream = await streamKnowledgeCards(contentToProcess)

    // 创建一个新的可读流，用于将AI流转换为SSE格式
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          // 发送基本信息作为第一个事件
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ type: 'basic_info', data: { title, content: contentToProcess } })}\n\n`))
          
          for await (const chunk of aiStream) {
            const content = chunk.choices[0]?.delta?.content || ''
            if (content) {
              // 将每个AI内容块作为SSE事件发送
              const payload = { type: 'ai_note', data: { content } }
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(payload)}\n\n`))
            }
          }
          // 发送完成信号，附带 isComplete 标记，便于前端最终处理
          const finalPayload = { type: 'ai_note', data: { content: '', isComplete: true } }
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(finalPayload)}\n\n`))
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()
        } catch (error) {
          console.error('Streaming error:', error)
          controller.error(error)
        }
      }
    })

    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('Error in process/stream:', error)
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
    return new Response(JSON.stringify({ error: 'Failed to process content', details: errorMessage }), { status: 500 })
  }
} 